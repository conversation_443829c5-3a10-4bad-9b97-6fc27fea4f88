ID;PRE/POST;ENVIRONMENT;FILENAME;REQUESTER;DONE DEVELOPMENT;DONE UAT;DONE STAGING;DONE BFXDEVELOPMENT;DONE BFXUAT;DONE PROD
<<<<<<< releases/release_20250501/rental/MP.csv
1;PRE;ALL;20250507_Unipol Rental CS Standard User_ProfileCreation.txt;<EMAIL>;Y;Y;Y;Y;Y;N
2;PRE;ALL;urcs_CRM-135_EntitlementProcess_MP_PRE.txt;<EMAIL>;Y;Y;Y;Y;Y;N
3;POST;ALL;urcs_CRM-135_EntitlementProcess_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
4;POST;ALL;urcs_CRM-78_EmailToCase_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
5;POST;ALL;urcs_Profile visibilities_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
6;POST;DEV;urcs_CRM-78_EmailToCase_MP_POST_DEV.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
7;POST;ALL;urcs_CRM-102_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
8;POST;DEV;urcs_EsecuzioneScriptUtenti20250710_MP_POST_DEV.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
9;POST;ALL;urcs_CRM-78ter_EmailToCaseRename_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
10;POST;ALL;urcs_CRM-162_MP_POST.txt;<EMAIL>;Y;Y;Y;Y;Y;N
11;POST;DEV;urcs_EsecuzioneScriptUtenti20250711_MP_POST_DEV.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
12;POST;DEV;urcs_EsecuzioneScriptUtenti20250715_MP_POST_DEV.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
13;POST;UAT;urcs_EsecuzioneScriptUtenti20250716_MP_POST_UAT.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
14;POST;ALL;urcs_MulesoftPermSet_MP_POST.txt;<EMAIL>;Y;Y;Y;N;N;N
15;POST;UAT;urcs_EsecuzioneScriptUtenti20250718_MP_POST_UAT.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
16;POST;UAT;urcs_EsecuzioneScriptUtenti20250722_MP_POST_UAT.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
17;POST;ALL;urcs_CRM-238_MP_POST.txt;<EMAIL>;Y;Y;N;N;N;N
18;POST;ALL;urcs_CRM-238Bis_MP_POST.txt;<EMAIL>;Y;Y;N;N;N;N
19;POST;UAT;urcs_EsecuzioneScriptUtenti20250722_bis_MP_POST_UAT.txt;<EMAIL>;Y;Y;Y;Y;Y;Y
20;POST;ALL;urcs_PermSetSystemUser.txt;<EMAIL>;Y;Y;N;N;N;N
21;POST;ALL;urcs_ChangeEmail-to-Case Settings.txt;<EMAIL>;Y;Y;N;N;N;N