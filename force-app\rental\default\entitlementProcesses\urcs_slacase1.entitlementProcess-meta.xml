<?xml version="1.0" encoding="UTF-8"?>
<EntitlementProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <SObjectType>Case</SObjectType>
    <active>true</active>
    <businessHours>Unipol Rental CS - Orari di lavoro</businessHours>
    <entryStartDateField>Case.CreatedDate</entryStartDateField>
    <exitCriteriaFilterItems>
        <field>Case.IsClosed</field>
        <operation>equals</operation>
        <value>true</value>
    </exitCriteriaFilterItems>
    <milestones>
        <criteriaBooleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11 AND 12 AND 13 AND 14 AND 15 AND 16 AND 17 AND 18 AND 19 AND 20 AND 21 AND 22 AND 23 AND 24 AND 25 AND 26 AND 27 AND 28 AND 29 AND 30 AND 31 AND 32 AND 33 AND 34 AND 35 AND 36 AND 37</criteriaBooleanFilter>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Richiesta autorizzazione rent</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi recupero credenziali AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Errori in accesso AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi gestione ruoli driver</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi recupero documenti su AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Errori applicativi AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Verifica dati mancanti AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Danni rientro-Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Conguaglio km Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Elementi mancanti al rientro - Cont. Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Auto restituita - Ritardo interruzione fatturazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata consegna</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo consegna</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Categoria Inferiore</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Tempistiche di riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Contestazione riaddebito riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Qualità Riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo sostituzione pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Sostituzione pneumatici non soddisfacente</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo trasferimento su fornitore</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Pneumatici -Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Tempi di riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Contestazione riaddebito Penali</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Qualità riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata autorizzazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancato Soccorso</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata erogazione veicolo sostitutivo</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Goal</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneGoal</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
    <milestones>
        <criteriaBooleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11 AND 12 AND 13 AND 14 AND 15 AND 16 AND 17 AND 18 AND 19 AND 20 AND 21 AND 22 AND 23 AND 24 AND 25 AND 26 AND 27 AND 28 AND 29 AND 30 AND 31 AND 32 AND 33 AND 34 AND 35 AND 36 AND 37</criteriaBooleanFilter>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Richiesta autorizzazione rent</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi recupero credenziali AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Errori in accesso AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi gestione ruoli driver</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Problemi recupero documenti su AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Errori applicativi AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Verifica dati mancanti AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Danni rientro-Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Conguaglio km Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Elementi mancanti al rientro - Cont. Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Auto restituita - Ritardo interruzione fatturazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata consegna</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo consegna</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Categoria Inferiore</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Tempistiche di riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Contestazione riaddebito riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Qualità Riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo sostituzione pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Sostituzione pneumatici non soddisfacente</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo trasferimento su fornitore</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Pneumatici -Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Tempi di riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Contestazione riaddebito Penali</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Qualità riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata autorizzazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Ritardo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancato Soccorso</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Mancata erogazione veicolo sostitutivo</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Deadline</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneDeadline</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
</EntitlementProcess>
